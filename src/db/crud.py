"""Database CRUD operations."""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from sqlalchemy import or_, desc

from .base import get_db
from .models import Task, Chatbot, UserChatHistory

# Define timeout duration (in minutes)
TASK_TIMEOUT_MINUTES = 10


def get_task(task_id: str) -> Optional[Dict[str, Any]]:
    """Retrieve a task from the database (excluding soft deleted)."""
    db = get_db()
    try:
        task = db.query(Task).filter(
            Task.task_id == task_id,
            Task.deleted_at.is_(None)
        ).first()
        if task:
            # Convert SQLAlchemy model to dict
            result = {
                "task_id": task.task_id,
                "task_type": task.task_type,
                "input_data": task.input_data,
                "status": task.status,
                "result": task.result,
                "created_at": task.created_at,
                "updated_at": task.updated_at
            }
            # print('task:', result)
            return result
        return None
    finally:
        db.close()


def create_task(task_id: str, task_type: str, input_data: str) -> None:
    """Create a new task in the database."""
    db = get_db()
    try:
        task = Task(
            task_id=task_id,
            task_type=task_type,
            input_data=input_data,
            status="queued",
            result=None
        )
        db.add(task)
        db.commit()
    finally:
        db.close()


def update_task(task_id: str, status: str, result: Optional[Dict[str, Any]] = None) -> None:
    """Update a task's status and result in the database."""
    db = get_db()
    print(
        f"Updating task {task_id} to status '{status}'")
    try:
        task = db.query(Task).filter(
            Task.task_id == task_id,
            Task.status != 'failed',
            Task.deleted_at.is_(None)
        ).first()

        if task:
            task.status = status
            task.result = result
            db.commit()
    finally:
        db.close()


def check_for_timed_out_tasks() -> int:
    """Check for tasks that have been in 'queued' or 'processing' status too long and mark them as failed."""
    db = get_db()
    try:
        # Calculate cutoff time
        cutoff_time = datetime.utcnow() - timedelta(minutes=TASK_TIMEOUT_MINUTES)

        # Find timed out tasks (excluding soft deleted)
        timed_out_tasks = db.query(Task).filter(
            or_(Task.status == 'queued', Task.status == 'processing'),
            Task.created_at < cutoff_time,
            Task.deleted_at.is_(None)
        ).all()

        # Mark each as failed
        count = 0
        for task in timed_out_tasks:
            print(f"Marking task {task.task_id} as failed due to timeout")
            task.status = 'failed'
            task.result = {
                "error": f"Task timed out after {TASK_TIMEOUT_MINUTES} minutes"
            }
            count += 1

        db.commit()
        return count
    finally:
        db.close()


# Chatbot CRUD functions

def create_or_update_chatbot(bot_id: str, name: str, persona: str, bot_type: Optional[str] = None, image_url: Optional[str] = None, voice: Optional[str] = None, voice_speed: Optional[float] = None, hello_message: Optional[str] = None, extra_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Create a new chatbot or update an existing one."""
    db = get_db()
    try:
        # Check if chatbot with that ID already exists (excluding soft deleted)
        chatbot = db.query(Chatbot).filter(
            Chatbot.id == bot_id,
            Chatbot.deleted_at.is_(None)
        ).first()

        if chatbot:
            # Update existing chatbot
            chatbot.name = name
            chatbot.persona = persona
            if bot_type is not None:
                chatbot.bot_type = bot_type
            if image_url is not None:
                chatbot.image_url = image_url
            if voice is not None:
                chatbot.voice = voice
            if voice_speed is not None:
                # Store as string for consistency
                chatbot.voice_speed = str(voice_speed)
            if hello_message is not None:
                chatbot.hello_message = hello_message
            if extra_data is not None:
                chatbot.extra_data = extra_data
            chatbot.updated_at = datetime.utcnow()
        else:
            # Create new chatbot
            chatbot = Chatbot(
                id=bot_id,
                name=name,
                persona=persona,
                bot_type=bot_type or "normal",
                image_url=image_url,
                voice=voice or "en_female_amanda_mars_bigtts",
                voice_speed=str(
                    voice_speed) if voice_speed is not None else "1.0",
                hello_message=hello_message or "Hello! How can I assist you today?",
                extra_data=extra_data
            )
            db.add(chatbot)

        db.commit()
        return {
            "id": chatbot.id,
            "name": chatbot.name,
            "persona": chatbot.persona,
            "bot_type": chatbot.bot_type,
            "image_url": chatbot.image_url,
            "voice": chatbot.voice,
            "voice_speed": float(chatbot.voice_speed) if chatbot.voice_speed else 1.0,
            "hello_message": chatbot.hello_message,
            "extra_data": chatbot.extra_data,
            "created_at": chatbot.created_at,
            "updated_at": chatbot.updated_at
        }
    finally:
        db.close()


def get_chatbot(bot_id: str) -> Optional[Dict[str, Any]]:
    """Retrieve a chatbot from the database (excluding soft deleted)."""
    db = get_db()
    try:
        chatbot = db.query(Chatbot).filter(
            Chatbot.id == bot_id,
            Chatbot.deleted_at.is_(None)
        ).first()
        if chatbot:
            return {
                "id": chatbot.id,
                "name": chatbot.name,
                "persona": chatbot.persona,
                "bot_type": chatbot.bot_type,
                "image_url": chatbot.image_url,
                "voice": chatbot.voice,
                "voice_speed": float(chatbot.voice_speed) if chatbot.voice_speed else 1.0,
                "hello_message": chatbot.hello_message,
                "extra_data": chatbot.extra_data,
                "created_at": chatbot.created_at,
                "updated_at": chatbot.updated_at
            }
        return None
    finally:
        db.close()


def get_all_chatbots() -> List[Dict[str, Any]]:
    """Retrieve all chatbots from the database (excluding soft deleted)."""
    db = get_db()
    try:
        chatbots = db.query(Chatbot).filter(
            Chatbot.deleted_at.is_(None)
        ).all()
        return [
            {
                "id": bot.id,
                "name": bot.name,
                "persona": bot.persona,
                "bot_type": bot.bot_type,
                "image_url": bot.image_url,
                "voice": bot.voice,
                "voice_speed": float(bot.voice_speed) if bot.voice_speed else 1.0,
                "hello_message": bot.hello_message,
                "extra_data": bot.extra_data,
                "created_at": bot.created_at,
                "updated_at": bot.updated_at
            }
            for bot in chatbots
        ]
    finally:
        db.close()


def delete_chatbot(bot_id: str) -> bool:
    """Soft delete a chatbot from the database."""
    db = get_db()
    try:
        chatbot = db.query(Chatbot).filter(
            Chatbot.id == bot_id,
            Chatbot.deleted_at.is_(None)
        ).first()
        if chatbot:
            chatbot.deleted_at = datetime.utcnow()
            db.commit()
            return True
        return False
    finally:
        db.close()


# User Chat History CRUD functions

def create_chat_history(message_id: str, bot_id: str, user_id: str, message: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new chat history entry. Ignores if message_id already exists."""
    db = get_db()
    try:
        # Check if message_id already exists (excluding soft deleted)
        existing_chat = db.query(UserChatHistory).filter(
            UserChatHistory.id == message_id,
            UserChatHistory.deleted_at.is_(None)
        ).first()

        if existing_chat:
            # Return existing chat history if message_id already exists
            return {
                "id": existing_chat.id,
                "bot_id": existing_chat.bot_id,
                "user_id": existing_chat.user_id,
                "message": existing_chat.message,
                "created_at": existing_chat.created_at,
                "updated_at": existing_chat.updated_at
            }

        # Create new chat history entry
        chat_history = UserChatHistory(
            id=message_id,
            bot_id=bot_id,
            user_id=user_id,
            message=message
        )
        db.add(chat_history)
        db.commit()

        return {
            "id": chat_history.id,
            "bot_id": chat_history.bot_id,
            "user_id": chat_history.user_id,
            "message": chat_history.message,
            "created_at": chat_history.created_at,
            "updated_at": chat_history.updated_at
        }
    finally:
        db.close()


def get_chat_history(message_id: str) -> Optional[Dict[str, Any]]:
    """Retrieve a chat history entry by ID (excluding soft deleted)."""
    db = get_db()
    try:
        chat_history = db.query(UserChatHistory).filter(
            UserChatHistory.id == message_id,
            UserChatHistory.deleted_at.is_(None)
        ).first()

        if chat_history:
            return {
                "id": chat_history.id,
                "bot_id": chat_history.bot_id,
                "user_id": chat_history.user_id,
                "message": chat_history.message,
                "created_at": chat_history.created_at,
                "updated_at": chat_history.updated_at
            }
        return None
    finally:
        db.close()


def get_user_chat_history(user_id: str, limit: Optional[int] = 50, offset: Optional[int] = 0) -> List[Dict[str, Any]]:
    """Retrieve chat history for a specific user (excluding soft deleted), ordered by created_at desc."""
    db = get_db()
    try:
        query = db.query(UserChatHistory).filter(
            UserChatHistory.user_id == user_id,
            UserChatHistory.deleted_at.is_(None)
        ).order_by(desc(UserChatHistory.created_at))

        if offset:
            query = query.offset(offset)
        if limit:
            query = query.limit(limit)

        chat_histories = query.all()

        return [
            {
                "id": chat.id,
                "bot_id": chat.bot_id,
                "user_id": chat.user_id,
                "message": chat.message,
                "created_at": chat.created_at,
                "updated_at": chat.updated_at
            }
            for chat in chat_histories
        ]
    finally:
        db.close()


def get_chat_history_by_before_timestamp(user_id: str, before_timestamp: datetime, limit: Optional[int] = 50) -> List[Dict[str, Any]]:
    """Retrieve chat history for a specific user before a certain timestamp (excluding soft deleted), ordered by created_at desc."""
    db = get_db()
    try:
        query = db.query(UserChatHistory).filter(
            UserChatHistory.user_id == user_id,
            UserChatHistory.created_at < before_timestamp,
            UserChatHistory.deleted_at.is_(None)
        ).order_by(desc(UserChatHistory.created_at))

        if limit:
            query = query.limit(limit)

        chat_histories = query.all()

        return [
            {
                "id": chat.id,
                "bot_id": chat.bot_id,
                "user_id": chat.user_id,
                "message": chat.message,
                "created_at": chat.created_at,
                "updated_at": chat.updated_at
            }
            for chat in chat_histories
        ]
    finally:
        db.close()


def update_chat_history(message_id: str, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Update a chat history entry's message."""
    db = get_db()
    try:
        chat_history = db.query(UserChatHistory).filter(
            UserChatHistory.id == message_id,
            UserChatHistory.deleted_at.is_(None)
        ).first()

        if chat_history:
            chat_history.message = message
            chat_history.updated_at = datetime.utcnow()
            db.commit()

            return {
                "id": chat_history.id,
                "bot_id": chat_history.bot_id,
                "user_id": chat_history.user_id,
                "message": chat_history.message,
                "created_at": chat_history.created_at,
                "updated_at": chat_history.updated_at
            }
        return None
    finally:
        db.close()


def delete_chat_history(user_id: str, message_id: str) -> bool:
    """Soft delete a chat history entry."""
    db = get_db()
    try:
        chat_history = db.query(UserChatHistory).filter(
            UserChatHistory.user_id == user_id,
            UserChatHistory.id == message_id,
            UserChatHistory.deleted_at.is_(None)
        ).first()

        if chat_history:
            chat_history.deleted_at = datetime.utcnow()
            db.commit()
            return True
        return False
    finally:
        db.close()


def delete_user_chat_history(user_id: str) -> int:
    """Soft delete all chat history entries for a specific user."""
    db = get_db()
    try:
        chat_histories = db.query(UserChatHistory).filter(
            UserChatHistory.user_id == user_id,
            UserChatHistory.deleted_at.is_(None)
        ).all()

        count = 0
        for chat_history in chat_histories:
            chat_history.deleted_at = datetime.utcnow()
            count += 1

        db.commit()
        return count
    finally:
        db.close()


def get_chat_history_count(user_id: str) -> int:
    """Get the count of chat history entries for a specific user (excluding soft deleted)."""
    db = get_db()
    try:
        count = db.query(UserChatHistory).filter(
            UserChatHistory.user_id == user_id,
            UserChatHistory.deleted_at.is_(None)
        ).count()
        return count
    finally:
        db.close()


def get_bot_chat_history(bot_id: str, user_id: str, before_timestamp: Optional[datetime] = None, limit: Optional[int] = 50) -> List[Dict[str, Any]]:
    """Get chat history for a specific bot and user, optionally filtered by timestamp."""
    db = get_db()
    try:
        query = db.query(UserChatHistory).filter(
            UserChatHistory.bot_id == bot_id,
            UserChatHistory.user_id == user_id,
            UserChatHistory.deleted_at.is_(None)
        )

        if before_timestamp:
            query = query.filter(UserChatHistory.created_at < before_timestamp)

        query = query.order_by(desc(UserChatHistory.created_at))

        if limit:
            query = query.limit(limit)

        chat_histories = query.all()

        return [
            {
                "id": chat.id,
                "bot_id": chat.bot_id,
                "user_id": chat.user_id,
                "message": chat.message,
                "created_at": chat.created_at,
                "updated_at": chat.updated_at
            }
            for chat in chat_histories
        ]
    finally:
        db.close()


def update_chat_history_with_api_result(user_id: str, message_id: str, api_type: str, api_result: Dict[str, Any]) -> bool:
    """Update chat history with API processing result following ChatMessageItem schema."""
    if not message_id:
        print("Message ID is required for updating chat history.")
        return False

    db = get_db()
    try:
        # Find the chat history entry
        chat_history = db.query(UserChatHistory).filter(
            UserChatHistory.id == message_id,
            UserChatHistory.user_id == user_id,
            UserChatHistory.deleted_at.is_(None)
        ).first()

        if not chat_history:
            print(
                f"Chat history with message_id {message_id} not found for user {user_id}.")
            return False

        # Get the current message
        message = chat_history.message or {}

        # Update the message with API result following ChatMessageItem schema
        if api_type == "asr":
            # For ASR, store the transcribed text
            if isinstance(api_result, dict) and "result" in api_result:
                text = api_result["result"].get("text", "")
                message["asr"] = text
            elif isinstance(api_result, str):
                message["asr"] = api_result

        elif api_type == "tts":
            # For TTS, store the audio URL or base64 data
            if isinstance(api_result, dict):
                if "audio_base64" in api_result:
                    message["tts"] = api_result["audio_base64"]
                elif "audio_url" in api_result:
                    message["tts"] = api_result["audio_url"]

        elif api_type == "translate":
            # For translation, store the translated text
            if isinstance(api_result, dict) and "translated_text" in api_result:
                message["translation"] = api_result["translated_text"]

        elif api_type == "grammar_suggestion":
            # For grammar analysis, store the suggestions object
            message["grammar_analysis"] = api_result

        elif api_type == "pronunciation_assessment":
            # For pronunciation assessment, store the assessment result
            message["pronunciation_assessment"] = api_result

        db.query(
            UserChatHistory
        ).filter(
            UserChatHistory.id == message_id,
            UserChatHistory.user_id == user_id,
            UserChatHistory.deleted_at.is_(None)
        ).update(
            {
                "message": message,
                "updated_at": datetime.now()
            }
        )

        print(
            f"Updating chat history with API result for user: {user_id}, message_id: {message_id}, api_type: {api_type}, api_result: {api_result}")

        db.commit()
        return True

    except Exception as e:
        db.rollback()
        print(f"Error updating chat history with API result: {e}")
        return False
    finally:
        db.close()
