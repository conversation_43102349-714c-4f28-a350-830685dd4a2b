from pydantic import BaseModel, <PERSON>, model_validator
from typing import Dict, Optional, Any, List, Literal
from datetime import datetime


class ASRTaskCreate(BaseModel):
    """Model for creating a new ASR task (not used directly in API but useful for documentation)"""
    file_name: str
    file_path: str


class ASRTaskResponse(BaseModel):
    """Response model when creating a new ASR task"""
    task_id: str
    status: str = Field(..., description="Initial status is 'queued'")


class Word(BaseModel):
    """Model representing a word in the ASR result"""
    text: str
    start_time: float = Field(...,
                              description="Start time in milliseconds")
    end_time: float = Field(...,
                            description="End time in milliseconds")
    confidence: float


class Utterance(BaseModel):
    """Model representing an utterance in the ASR result"""
    text: str
    start_time: float = Field(...,
                              description="Start time in milliseconds")
    end_time: float = Field(...,
                            description="End time in milliseconds")
    words: List[Word]


class ASRResultAdditions(BaseModel):
    """Model representing additions in the ASR result"""
    duration: str


class ASRTaskResult(BaseModel):
    """Model representing the result of an ASR task"""
    text: str = Field(..., description="Transcription text")
    additions: Optional[ASRResultAdditions] = Field(
        None, description="Additions to the transcription result")
    utterances: Optional[List[Utterance]] = Field(
        None, description="List of utterances in the transcription")
    srt: Optional[str] = Field(
        None, description="SRT formatted string if available",
        example="1\n00:00:01,000 --> 00:00:05,000\nHello, world!\n\n2\n00:00:06,000 --> 00:00:10,000\nHow are you?")


class ASRTaskStatus(BaseModel):
    """Response model when checking the status of an ASR task"""
    task_id: str
    status: str = Field(...,
                        description="One of: 'queued', 'processing', 'completed', 'failed'")
    result: Optional[ASRTaskResult | str] = Field(
        None, description="Results from the ASR processing when completed")
    error: Optional[str] = Field(
        None, description="Error message if the task failed")
    created_at: datetime
    updated_at: datetime


class AudioInfo(BaseModel):
    """Model representing audio information in the ASR result"""
    duration: int


class ASRResult(BaseModel):
    """Model representing the structured ASR result from the service"""
    result: Dict[str, Any] = Field(...,
                                   description="Result containing text and utterances")
    audio_info: AudioInfo = Field(...,
                                  description="Audio information including duration")

    # Note: These methods are currently commented out due to type annotation issues
    # They can be uncommented and fixed if needed for the ASR functionality
    # def get_transcript(self) -> str:
    #     """Get the full transcription text"""
    #     return self.result.get("text", "")
    #
    # def get_duration_ms(self) -> int:
    #     """Get the audio duration in milliseconds"""
    #     return self.audio_info.duration
    #
    # def get_segments(self) -> List[Dict[str, Any]]:
    #     """Get the time-aligned segments of the transcription"""
    #     return self.result.get("utterances", [])


class PronunciationPhoneme(BaseModel):
    phoneme: str
    accuracy_score: float


class PronunciationWord(BaseModel):
    word: str
    accuracy_score: float
    error_type: Optional[str] = None
    phonemes: List[PronunciationPhoneme]


class PronunciationAssessmentResult(BaseModel):
    recognized_text: str
    reference_text: str
    pronunciation_score: float
    accuracy_score: float
    fluency_score: float
    completeness_score: float
    words: List[PronunciationWord]


class PronunciationAssessmentResponse(BaseModel):
    status: str = Field(..., description="Status of the assessment")
    error_message: Optional[str] = Field(
        None, description="Error message if the assessment failed")
    result: Optional[PronunciationAssessmentResult] = Field(
        None, description="Detailed result of the pronunciation assessment")


class PronunciationAssessmentRequest(BaseModel):
    reference_text: str
    language: str = "en-US"
    # Note: The audio file is handled through Form/File in FastAPI and not in this model


class ChatMessage(BaseModel):
    role: str
    content: str


class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: Optional[str] = "doubao-1-5-pro-32k-250115"
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False
    tts: Optional[bool] = False
    tts_voice: Optional[str] = None


class ChatResponse(BaseModel):
    id: str
    object: str
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]


class LLMChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: Optional[str] = "doubao-1-5-pro-32k-250115"
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False


class LLMChatResponse(BaseModel):
    id: str
    object: str
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]


class ErrorResponse(BaseModel):
    """Model for error responses"""
    error: str = Field(..., description="Error message")
    details: Optional[str] = None


class LLMChatModel(BaseModel):
    id: str
    object: str
    created: int
    owned_by: str
    root: Optional[str] = None
    parent: Optional[str] = None


class LLMChatModelsResponse(BaseModel):
    object: str = Field(..., description="Object type, usually 'list'")
    data: List[LLMChatModel] = Field(...,
                                     description="List of available LLM models")


class GrammarSuggestions(BaseModel):
    errors: List[str]
    improvements: List[str]
    suggested_text: str


class GrammarSuggestionRequest(BaseModel):
    text: str
    coloquial: Optional[bool] = False
    message_id: Optional[str] = None


class GrammarSuggestionResponse(BaseModel):
    task_id: str
    status: str
    suggestions: Optional[GrammarSuggestions] = None


# --- New TTS Models ---

class TTSRequest(BaseModel):
    text: str
    voice: Optional[str] = "zh_female_shuangkuaisisi_emo_v2_mars_bigtts"
    speed: Optional[float] = 1.0
    stream: Optional[bool] = False
    message_id: Optional[str] = None


class TTSResponse(BaseModel):
    task_id: str
    status: str
    audio_base64: Optional[str] = None
    audio_url: Optional[str] = None

#   {
#     "scene": "多情感",
#     "name": "柔美女友（多情感）",
#     "voice_type": "zh_female_roumeinvyou_emo_v2_mars_bigtts",
#     "timestamp": "√",
#     "language": "中文",
#     "emotion": "happy,sad,angry,surprised,fear,hate,excited,coldness,neutral"
#   },


class TTSVoice(BaseModel):
    scene: str = Field(..., description="Scene of the voice")
    name: str = Field(..., description="Name of the voice")
    voice_type: str = Field(..., description="Voice type identifier")
    # timestamp: Optional[str] = Field(
    # None, description="Timestamp availability")
    language: str = Field(..., description="Language of the voice")
    emotion: str = Field(..., description="Emotional tone of the voice")


class TTSVoiceResponse(BaseModel):
    status: str = Field(..., description="Status of the voice retrieval")
    voices: List[TTSVoice] = Field(...,
                                   description="List of available TTS voices")

# Define request/response models for the translation API


class TranslateRequest(BaseModel):
    before_text: Optional[str] = ""
    text: str
    message_id: Optional[str] = None


class TranslateResponse(BaseModel):
    task_id: str
    status: str
    translated_text: Optional[str] = None


class ChatbotRequest(BaseModel):
    id: str
    name: str
    persona: str
    # Add bot type field with validation
    bot_type: Optional[Literal["normal", "mission"]] = "normal"
    image_url: Optional[str] = None
    voice: Optional[str] = None
    voice_speed: Optional[float] = 1.0  # Add voice speed field
    hello_message: Optional[str] = None  # Add hello message field
    extra_data: Optional[Dict[str, Any]] = None  # Add extra data field


class ChatbotResponse(BaseModel):
    id: str
    name: str
    persona: str
    # Add bot type field with validation
    bot_type: Optional[Literal["normal", "mission"]] = "normal"
    image_url: Optional[str] = None
    voice: Optional[str] = None
    voice_speed: Optional[float] = 1.0  # Add voice speed field
    hello_message: Optional[str] = None  # Add hello message field
    extra_data: Optional[Dict[str, Any]] = None  # Add extra data field
    created_at: datetime
    updated_at: datetime


class DeleteChatbotResponse(BaseModel):
    success: bool
    message: str = "Chatbot deleted successfully"


class ChatbotListResponse(BaseModel):
    chatbots: List[ChatbotResponse]


class ChatSuggestRequest(BaseModel):
    conversation_history: List[Dict[str, Any]] = []
    context: Optional[str] = None


class ChatSuggestResponse(BaseModel):
    suggestions: List[str]


class ConversationSummaryRequest(BaseModel):
    messages: List[dict]
    bot_name: Optional[str] = None
    grammar_results: Optional[Any] = None
    pronunciation_results: Optional[Any] = None


class GrammarAnalysisData(BaseModel):
    total_messages_analyzed: int
    total_errors_found: int
    total_improvements_suggested: int
    average_errors_per_message: float
    messages_with_analysis: List[dict]


class PronunciationAnalysisData(BaseModel):
    total_voice_messages: int
    average_pronunciation_score: float
    average_accuracy_score: float
    average_fluency_score: float
    voice_messages_analyzed: List[dict]


class ConversationSummary(BaseModel):
    overall_summary: str
    english_level: str
    grammar: list[str]
    pronunciation: list[str]
    topic_relevance: list[str]
    feedback: list[str]
    english_level_score: int


class ConversationSummaryResponse(BaseModel):
    task_id: str
    status: str
    summary: Optional[ConversationSummary] = None


class MissionStep(BaseModel):
    action: str
    status: str = Field(
        default="pending", description="Status of the step: pending, in_progress, completed, failed")


class MissionRequest(BaseModel):
    mission_details: str = Field(...,
                                 description="Details of the mission to be created")


class MissionData(BaseModel):
    title: str
    description: str
    objectives: List[str]
    steps: List[MissionStep]


class MissionResponse(BaseModel):
    mission: Optional[MissionData] = None


class MissionCheckRequest(BaseModel):
    mission: MissionData = Field(...,
                                 description="The mission to check status for")
    conversation_history: str = Field(...,
                                      description="The conversation history to analyze")


class MissionCheckResponse(BaseModel):
    updated_mission: Optional[MissionData] = None


class ChatMessageItem(BaseModel):
    """Model representing a single chat message item"""
    role: str
    content: str
    type: Optional[Literal["text", "audio", "image"]] = "text"
    asr: Optional[str] = None
    duration: Optional[float] = None  # Duration in seconds for audio messages
    tts: Optional[str] = None
    translation: Optional[str] = None
    grammar_analysis: Optional[GrammarSuggestions] = None
    pronunciation_assessment: Optional[PronunciationAssessmentResult] = None

    @model_validator(mode='after')
    def extract_voice_message_info(self):
        """Auto-extract duration from voice messages in content"""
        if self.content and self.type == "text" and not self.duration:
            try:
                import json
                parsed_content = json.loads(self.content)
                if parsed_content.get("type") == "voice" and "duration" in parsed_content:
                    self.duration = parsed_content["duration"]
                    self.type = "audio"
            except (json.JSONDecodeError, TypeError):
                pass
        return self


class ChatHistoryItem(BaseModel):
    """Model representing a single chat history item"""
    id: str
    bot_id: str
    user_id: int
    message: ChatMessageItem
    created_at: datetime
    updated_at: datetime


class ChatHistoryResponse(BaseModel):
    """Response model for chat history endpoints"""
    status: str = "success"
    data: List[ChatHistoryItem]
    total_count: Optional[int] = None


class DeleteChatHistoryResponse(BaseModel):
    """Response model for deleting chat history"""
    status: str = "success"
    message: str


class CreateChatHistoryRequest(BaseModel):
    message_id: str
    bot_id: str
    message: ChatMessageItem


class CreateChatHistoryResponse(BaseModel):
    status: str = "success"
    message_id: str
# Elsa Pronunciation Assessment Models


class ElsaGeneralScores(BaseModel):
    elsa: Dict[str, Any] = Field(default_factory=dict)
    cefr: Dict[str, Any] = Field(default_factory=dict)
    other_scores: Dict[str, Any] = Field(default_factory=dict)


class ElsaFluencyMetrics(BaseModel):
    words_per_minute: Optional[float] = None
    words_per_minute_min: Optional[float] = None
    words_per_minute_max: Optional[float] = None
    pausing_score: Optional[float] = None


class ElsaPronunciationMetrics(BaseModel):
    advanced_pronunciation_score: Optional[float] = None


class ElsaIntonationMetrics(BaseModel):
    pitch_variation: Optional[float] = None
    energy_variation: Optional[float] = None
    word_prominence_score: Optional[float] = None
    word_prominence_decision: Optional[str] = None
    word_prominence_cefr: Optional[str] = None


class ElsaCefrDistribution(BaseModel):
    cefr_level: str
    percentage: float


class ElsaVocabularyMetrics(BaseModel):
    total_words_count: int
    unique_words_count: int
    uncommon_words_count: int
    cefr_distribution: List[ElsaCefrDistribution]


class ElsaOtherMetrics(BaseModel):
    fluency: ElsaFluencyMetrics
    pronunciation: ElsaPronunciationMetrics
    intonation: ElsaIntonationMetrics
    vocabulary: ElsaVocabularyMetrics


class ElsaMetrics(BaseModel):
    general_scores: ElsaGeneralScores
    other_metrics: ElsaOtherMetrics


class ElsaPronunciationFeedback(BaseModel):
    top_errors: Optional[Any] = None


class ElsaIntonationFeedback(BaseModel):
    word_prominence_items: Optional[Any] = None


class ElsaGrammarFeedback(BaseModel):
    items: List[Any] = Field(default_factory=list)


class ElsaVocabularyFeedback(BaseModel):
    top_cefr_words: List[Any] = Field(default_factory=list)


class ElsaFeedbacks(BaseModel):
    show_transcript: bool = False
    pronunciation: ElsaPronunciationFeedback
    intonation: ElsaIntonationFeedback
    grammar: ElsaGrammarFeedback
    vocabulary: ElsaVocabularyFeedback


class ElsaUtterance(BaseModel):
    utterance_id: int
    start_time: float
    end_time: float
    start_index: int
    end_index: int
    text: str
    result: Optional[Any] = None


class ElsaSpeaker(BaseModel):
    speaker_id: int
    total_time: float
    metrics: ElsaMetrics
    feedbacks: ElsaFeedbacks
    utterances: List[ElsaUtterance]


class ElsaTimeline(BaseModel):
    speaker_id: int
    utterance_id: int
    start_time: float
    end_time: float
    start_index: int
    end_index: int


class ElsaPronunciationAssessmentResponse(BaseModel):
    speakers: List[ElsaSpeaker]
    transcript: str
    timeline: List[ElsaTimeline]
    api_version: str
    api_plan: str
    recording_quality: str
    assessment_quality: str
    total_time: float
    success: bool
    status: str = "success"
    error_message: Optional[str] = None
