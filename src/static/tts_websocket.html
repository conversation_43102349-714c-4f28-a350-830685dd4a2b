<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WebSocket TTS Demo</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 800px;
      padding: 20px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .card {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .card-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 20px;
    }

    textarea {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 15px;
      width: 100%;
      min-height: 150px;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      resize: vertical;
    }

    textarea:focus {
      outline: none;
      border-color: #0056b3;
      box-shadow: 0 0 5px rgba(0, 86, 179, 0.3);
    }

    select {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 10px;
      width: 100%;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .btn-primary {
      background-color: #007bff;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      color: white;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      background-color: #0056b3;
      transform: translateY(-2px);
    }

    .btn-success {
      background-color: #28a745;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      color: white;
      transition: all 0.3s ease;
    }

    .btn-success:hover {
      background-color: #218838;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background-color: #6c757d;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      color: white;
      transition: all 0.3s ease;
    }

    .btn-secondary:hover {
      background-color: #545b62;
      transform: translateY(-2px);
    }

    .btn:disabled {
      background-color: #cccccc;
      transform: none;
      cursor: not-allowed;
    }

    .status-badge {
      background-color: #f1f1f1;
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 15px;
      font-weight: 500;
    }

    .log {
      height: 250px;
      overflow-y: auto;
      background-color: #f5f5f5;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-family: monospace;
      font-size: 0.9em;
    }

    .log-entry {
      padding: 5px 0;
      border-bottom: 1px solid #ececec;
    }

    .log-entry:last-child {
      border-bottom: none;
    }

    .audio-panel {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border-left: 5px solid #28a745;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">WebSocket TTS Demo</h1>
      <p class="lead text-muted">Real-time text-to-speech conversion with WebSockets</p>
    </div>

    <div class="card">
      <h2 class="card-title">Voice Configuration</h2>
      <div class="row mb-4">
        <div class="col-md-6">
          <label for="voice" class="form-label">Voice:</label>
          <select id="voice" class="form-select">
            <option value="en_female_amanda_mars_bigtts">English - Amanda</option>
            <option value="zh_female_qingqing_emo_v2_mars_bigtts">Chinese - Qingqing</option>
            <!-- Add more voice options as needed -->
          </select>
        </div>
        <div class="col-md-6">
          <label for="speed" class="form-label">Speed:</label>
          <input type="number" id="speed" value="1.0" min="0.5" max="2.0" step="0.1" class="form-control">
        </div>
      </div>

      <h2 class="card-title">Text Input</h2>
      <div class="mb-4">
        <textarea id="text" placeholder="Type or paste text here..." class="form-control"></textarea>
      </div>

      <div class="d-flex gap-2 mb-4">
        <button id="connect" class="btn btn-primary">Connect</button>
        <button id="send" class="btn btn-success" disabled>Send Text</button>
        <button id="stream" class="btn btn-secondary" disabled>Stream Mode</button>
        <button id="end" class="btn btn-secondary" disabled>End Input</button>
      </div>

      <div id="status" class="status-badge">Status: Disconnected</div>
    </div>

    <div class="card">
      <h2 class="card-title">Audio Output</h2>
      <div id="audio-container" class="audio-panel"></div>
    </div>

    <div class="card">
      <h2 class="card-title">Log</h2>
      <div id="log" class="log"></div>
    </div>
  </div>

  <footer>
    <p>© 2025 WebSocket TTS Demo | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Get DOM elements
    const voiceSelect = document.getElementById('voice');
    const speedInput = document.getElementById('speed');
    const textInput = document.getElementById('text');
    const connectBtn = document.getElementById('connect');
    const sendBtn = document.getElementById('send');
    const streamBtn = document.getElementById('stream');
    const endBtn = document.getElementById('end');
    const statusDiv = document.getElementById('status');
    const logDiv = document.getElementById('log');
    const audioContainer = document.getElementById('audio-container');

    let ws;
    let audioChunks = [];
    let isStreamMode = false;

    // Variable to track audio queue, playback state, and buffering
    let audioQueue = [];
    let isPlaying = false;
    let bufferingTimeout = null;
    const MIN_CHUNKS_TO_PLAY = 10;
    const BUFFER_TIMEOUT_MS = 500;

    // Log messages
    function log(message) {
      const entry = document.createElement('div');
      entry.className = 'log-entry';
      entry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
      logDiv.appendChild(entry);
      logDiv.scrollTop = logDiv.scrollHeight;
    }

    // Update status
    function updateStatus(message) {
      statusDiv.textContent = `Status: ${message}`;

      // Update status badge color based on the status
      if (message === 'Connected' || message === 'Ready') {
        statusDiv.style.backgroundColor = '#d4edda';
        statusDiv.style.color = '#155724';
        statusDiv.style.borderLeft = '5px solid #28a745';
      } else if (message === 'Disconnected') {
        statusDiv.style.backgroundColor = '#f1f1f1';
        statusDiv.style.color = '#383d41';
        statusDiv.style.borderLeft = '5px solid #6c757d';
      } else if (message.includes('Error')) {
        statusDiv.style.backgroundColor = '#f8d7da';
        statusDiv.style.color = '#721c24';
        statusDiv.style.borderLeft = '5px solid #dc3545';
      }
    }

    // Connect to WebSocket
    connectBtn.addEventListener('click', () => {
      connectBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Connecting...';

      ws = new WebSocket(`ws://${window.location.host}/api/tts_stream`);

      ws.onopen = () => {
        updateStatus('Connected');
        log('WebSocket connection established');

        // Send initialization data
        const initData = {
          voice: voiceSelect.value,
          speed_ratio: parseFloat(speedInput.value),
          emotion: ""
        };
        ws.send(JSON.stringify(initData));
        log(`Sent initialization data: ${JSON.stringify(initData)}`);

        connectBtn.disabled = true;
        connectBtn.textContent = 'Connected';
      };

      ws.onmessage = (event) => {
        if (event.data instanceof Blob) {
          // Audio chunk received
          handleAudioChunk(event.data);
          log(`Received audio chunk: ${event.data.size} bytes`);
        } else {
          // Status message received
          try {
            const message = JSON.parse(event.data);
            log(`Received message: ${JSON.stringify(message)}`);

            if (message.status === 'ready') {
              sendBtn.disabled = false;
              streamBtn.disabled = false;
              endBtn.disabled = false;
              updateStatus('Ready');
            }

            if (message.error) {
              log(`Error: ${message.error}`);
              updateStatus(`Error: ${message.error}`);
            }
          } catch (e) {
            log(`Received text message: ${event.data}`);
          }
        }
      };

      ws.onerror = (error) => {
        log(`WebSocket error: ${error}`);
        updateStatus('Error');
        connectBtn.disabled = false;
        connectBtn.textContent = 'Connect';
      };

      ws.onclose = (event) => {
        log(`WebSocket closed: ${event.code} ${event.reason}`);
        updateStatus('Disconnected');
        connectBtn.disabled = false;
        connectBtn.textContent = 'Connect';
        sendBtn.disabled = true;
        streamBtn.disabled = true;
        endBtn.disabled = true;
      };
    });

    // Handle audio chunks
    function handleAudioChunk(blob) {
      audioChunks.push(blob);

      if (isStreamMode) {
        // In stream mode, add to queue and start buffer management
        audioQueue.push(blob);
        manageStreamBuffer();
      }
    }

    // Manage streaming buffer - starts playback when conditions are met
    function manageStreamBuffer() {
      // Clear any existing timeout when we receive a new chunk
      if (bufferingTimeout) {
        clearTimeout(bufferingTimeout);
      }

      // If already playing, do nothing (the queue is being processed)
      if (isPlaying) {
        return;
      }

      // Start playback immediately if we have enough chunks
      if (audioQueue.length >= MIN_CHUNKS_TO_PLAY) {
        log(`Starting playback with ${audioQueue.length} chunks`);
        playNextChunk();
        return;
      }

      // Otherwise set a timeout to start playback after delay
      bufferingTimeout = setTimeout(() => {
        if (audioQueue.length > 0 && !isPlaying) {
          log(`Starting playback after timeout with ${audioQueue.length} chunks`);
          playNextChunk();
        }
      }, BUFFER_TIMEOUT_MS);
    }

    // Function to play the next chunk in queue
    function playNextChunk() {
      if (audioQueue.length === 0) {
        isPlaying = false;
        return;
      }

      isPlaying = true;

      // Take multiple chunks to merge before playing
      const mergedChunks = [];

      while (audioQueue.length > 0) {
        mergedChunks.push(audioQueue.shift());
      }

      // Create a single blob from multiple chunks
      const mergedBlob = new Blob(mergedChunks, { type: 'audio/mpeg' });
      const audioUrl = URL.createObjectURL(mergedBlob);
      const audio = new Audio(audioUrl);

      // Set up event handlers
      audio.onended = () => {
        URL.revokeObjectURL(audioUrl); // Clean up
        playNextChunk(); // Play next batch when current one ends
      };

      audio.onerror = (err) => {
        console.error('Audio playback error:', err);
        URL.revokeObjectURL(audioUrl);
        playNextChunk(); // Continue to next batch even if there's an error
      };

      // Log the merged playback
      log(`Playing merged audio: ${mergedChunks.length} chunks (${mergedBlob.size} bytes)`);

      // Start playback
      audio.play().catch(err => {
        console.error('Failed to play audio:', err);
        playNextChunk(); // Move to next batch if playback fails
      });
    }

    // Clear audio queue (useful when stopping or resetting)
    function clearAudioQueue() {
      audioQueue = [];
      isPlaying = false;
      if (bufferingTimeout) {
        clearTimeout(bufferingTimeout);
        bufferingTimeout = null;
      }
    }

    // Send text
    sendBtn.addEventListener('click', () => {
      const text = textInput.value.trim();
      if (text && ws && ws.readyState === WebSocket.OPEN) {
        sendBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Sending...';
        ws.send(text);
        log(`Sent text: ${text}`);
        textInput.value = '';
        setTimeout(() => {
          sendBtn.textContent = 'Send Text';
        }, 500);
      }
    });

    // Toggle stream mode
    streamBtn.addEventListener('click', () => {
      isStreamMode = !isStreamMode;
      streamBtn.textContent = isStreamMode ? 'Stream Mode: ON' : 'Stream Mode';
      streamBtn.className = isStreamMode ? 'btn btn-success' : 'btn btn-secondary';
      log(`Stream mode ${isStreamMode ? 'enabled' : 'disabled'}`);
    });

    // End input
    endBtn.addEventListener('click', () => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        endBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
        ws.send('<|END|>');
        log('Sent END signal');

        // After a short delay, combine and play all audio chunks
        setTimeout(() => {
          if (!isStreamMode && audioChunks.length > 0) {
            const combinedBlob = new Blob(audioChunks, { type: 'audio/mpeg' });
            const audioUrl = URL.createObjectURL(combinedBlob);

            const audio = document.createElement('audio');
            audio.controls = true;
            audio.style.width = '100%';
            audio.src = audioUrl;

            // Clear previous audio elements
            audioContainer.innerHTML = '';
            audioContainer.appendChild(audio);

            log(`Created combined audio (${combinedBlob.size} bytes)`);
            audioChunks = []; // Reset for next session
            endBtn.textContent = 'End Input';
          }
        }, 1000);
      }
    });
  </script>
</body>

</html>