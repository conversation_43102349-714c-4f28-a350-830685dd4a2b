stages:
  - build
  - deploy
  - notify

include:
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'templates/deploy-k8s.yaml'
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'jobs/notify-success.yaml'
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'jobs/notify-fail.yaml'

variables:
  GITOPS_APP_NAME: foundation-gusto-ai-base-service

workflow:
  # rules的规则：匹配到第一个，后面的短路
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      variables:
        IMAGE: registry.cn-shenzhen.aliyuncs.com/project5e-test/foundation-gusto-ai-base-service
        GITOPS_ENVIRONMENT: test
        DOCKER_USERNAME: $TEST_DOCKER_USERNAME
        DOCKER_PASSWORD: $TEST_DOCKER_PASSWORD
    - if: '$CI_COMMIT_BRANCH == "main"'
      variables:
        IMAGE: registry.cn-beijing.aliyuncs.com/4tune/foundation-gusto-ai-base-service
        GITOPS_ENVIRONMENT: prod
        DOCKER_USERNAME: $PROD_DOCKER_USERNAME
        DOCKER_PASSWORD: $PROD_DOCKER_PASSWORD

build:
  stage: build
  tags:
    - 4tune
    - beijing 
    - prod
  script:
    - docker build -t ${IMAGE} .
    - docker tag ${IMAGE} ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker login --username=${DOCKER_USERNAME} --password=${DOCKER_PASSWORD} ${IMAGE}
    - docker push ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker rmi ${IMAGE}:${CI_COMMIT_SHORT_SHA}

deploy:
  stage: deploy
  tags:
    - 4tune
    - beijing 
    - prod
  extends:
    - .deploy-stable
  environment:
    name: ${GITOPS_ENVIRONMENT}
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "develop"'

build-chat-app:
  stage: build
  tags:
    - 4tune
    - beijing 
    - prod
  script:
    - cd chat-app
    - yarn
    - yarn build-dev
    - echo CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN} > .env
    - npx --registry=https://registry.npm.taobao.org wrangler pages deploy ./build --project-name chat-app --branch $CI_COMMIT_BRANCH
    - rm -R build && rm .env
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"' 


build-chat-app-prod:
  stage: build
  tags:
    - 4tune
    - beijing 
    - prod
  script:
    - cd chat-app
    - yarn
    - yarn build-prod
    - echo CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN} > .env
    - npx --registry=https://registry.npm.taobao.org wrangler pages deploy ./build --project-name chat-app --branch $CI_COMMIT_BRANCH
    - rm -R build && rm .env
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"' 