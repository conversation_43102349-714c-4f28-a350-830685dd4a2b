"""Test runner script for the Gusto AI Base Service."""

import subprocess
import sys
import os


def run_tests():
    """Run all tests with coverage."""
    print("🧪 Running unit tests...")

    # Install test dependencies
    print("📦 Installing test dependencies...")
    result = subprocess.run([
        "uv", "pip", "install",
        "pytest>=8.0.0",
        "pytest-asyncio>=0.23.0",
        "pytest-mock>=3.12.0",
        "httpx>=0.27.0",
        "coverage>=7.4.0",
        "pytest-cov>=4.0.0"
    ], capture_output=True, text=True)

    if result.returncode != 0:
        print(f"❌ Failed to install test dependencies: {result.stderr}")
        return 1

    # Set test environment variables
    os.environ["ENVIRONMENT"] = "test"
    os.environ["API_KEY"] = "test-api-key"
    os.environ["DB"] = "sqlite:///test.db"

    # Add current directory to Python path for src module imports
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.environ["PYTHONPATH"] = current_dir

    # Run tests with coverage
    cmd = [
        "uv", "run", "pytest",
        "tests/",
        "--cov=src",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--cov-report=xml",
        "-v",
        "--asyncio-mode=auto"
    ]

    print(f"🔍 Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, env=os.environ.copy())

    if result.returncode == 0:
        print("✅ All tests passed!")
        print("📊 Coverage report generated in htmlcov/")
    else:
        print("❌ Some tests failed!")

    return result.returncode


def run_specific_test(test_file):
    """Run a specific test file."""
    print(f"🧪 Running tests in {test_file}...")

    cmd = [
        "uv", "run", "pytest",
        f"tests/{test_file}",
        "-v",
        "--asyncio-mode=auto"
    ]

    result = subprocess.run(cmd)
    return result.returncode


if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_file = sys.argv[1]
        exit_code = run_specific_test(test_file)
    else:
        exit_code = run_tests()

    sys.exit(exit_code)
