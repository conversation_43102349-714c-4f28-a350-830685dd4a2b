import React, { useState, useEffect, useRef } from "react";
import { marked } from "marked";
import hljs from "highlight.js";
import MessageToolbar from "./MessageToolbar";
import MessageResultPanel from "./MessageResultPanel";
import {
  handleGrammarCorrection,
  handleTranslation,
  handleASR,
  handlePronunciationAssessment, // Add this import
} from "../../services/messageServices";
import "./Message.css";
import TTSStreamPlayer from "../../services/TTSStreamPlayer";
import {
  ChatMessageSenderType,
  ChatSystemMessage,
} from "../../types/ChatMessageType";

const Message = ({
  message,
  autoTTSEnabled,
  apiKey,
  onDelete,
  onResend,
  isLastMessage,
}) => {
  const [showResultPanel, setShowResultPanel] = useState(false);
  const [resultType, setResultType] = useState(null);
  const [resultData, setResultData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isPlayingVoice, setIsPlayingVoice] = useState(false);
  const messageRef = useRef(null);
  const ttsPlayerRef = useRef(null);
  const audioRef = useRef(null);

  // Initialize TTS player once
  useEffect(() => {
    ttsPlayerRef.current = new TTSStreamPlayer();
    return () => {
      // Clean up on unmount
      if (ttsPlayerRef.current) {
        ttsPlayerRef.current.stop();
      }
      // Stop any playing voice message
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  // useEffect(() => {
  //   // Auto-TTS for new bot messages if enabled, but skip "Thinking..." messages
  //   if (
  //     autoTTSEnabled &&
  //     message.sender === "bot" &&
  //     message.text &&
  //     message.text.trim() !== "Thinking..." &&
  //     message.type !== "audio"
  //   ) {
  //     handleTTSAction();
  //   }
  // }, []);

  useEffect(() => {
    // Apply syntax highlighting to code blocks
    if (messageRef.current) {
      const codeBlocks = messageRef.current.querySelectorAll("pre code");
      if (codeBlocks) {
        codeBlocks.forEach((block) => {
          hljs.highlightElement(block);
        });
      }
    }
    if (message.sender === ChatMessageSenderType.BOT) {
      // console.log("Message updated:", message);
      // Only show loading/result panel during typing or audio playback
      if (message.isTyping || message.isPlayingAudio) {
        setShowResultPanel(true);
        setIsLoading(true);
      } else {
        // Message is completed - hide the result panel and loading state
        setShowResultPanel(false);
        setIsLoading(false);
        setResultType(null);
        setResultData(null);
      }
    }
  }, [message, message.isTyping, message.isPlayingAudio]);

  const handleTTSAction = async () => {
    // If result panel is currently showing TTS, close it
    if (resultType === "tts" && showResultPanel) {
      ttsPlayerRef.current.stop();
      setShowResultPanel(false);
      return;
    }

    setResultType("tts");
    setShowResultPanel(true);

    // Use the TTS player class to handle all TTS logic
    ttsPlayerRef.current.playTTS(
      message.id,
      message.text || "",
      // Loading state callback
      (isLoading) => setIsLoading(isLoading),
      // Playback complete callback
      () => {
        setShowResultPanel(false);
      }
    );
  };

  const getASRResult = async () => {
    if (message.asr) {
      return message.asr;
    }

    const result = await handleASR({
      messageId: message.id,
      audioUrl,
      apiKey,
    });
    return result.text || "";
  };

  const handleGrammarAction = async () => {
    if (resultType === "grammar" && showResultPanel) {
      // Toggle off if already showing
      setShowResultPanel(false);
      return;
    }

    setResultType("grammar");
    setShowResultPanel(true);
    setIsLoading(true);

    try {
      let text = message.content || "";
      const isVoiceMsg = message.type === "audio";
      if (isVoiceMsg) {
        text = await getASRResult();
      }
      let result = message.grammar_analysis;
      if (!result) {
        result = await handleGrammarCorrection({
          text: isVoiceMsg ? text : message.text,
          coloquial: isVoiceMsg,
          apiKey,
        });
        result = result.suggestions;
      }
      setResultData(result);
    } catch (error) {
      console.error("Grammar error:", error);
      setResultData({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTranslationAction = async () => {
    if (resultType === "translation" && showResultPanel) {
      // Toggle off if already showing
      setShowResultPanel(false);
      return;
    }

    setResultType("translation");
    setShowResultPanel(true);
    setIsLoading(true);

    let text = message.content || "";
    if (message.type === "audio") {
      // If it's a voice message, get the ASR result first
      text = await getASRResult();
    }

    try {
      const result = await handleTranslation({
        messageId: message.id,
        text,
        apiKey,
      });
      setResultData(result);
    } catch (error) {
      console.error("Translation error:", error);
      setResultData({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  // Add ASR handler
  const handleASRAction = async () => {
    if (resultType === "asr" && showResultPanel) {
      // Toggle off if already showing
      setShowResultPanel(false);
      return;
    }

    setResultType("asr");
    setShowResultPanel(true);
    setIsLoading(true);

    console.log("Handling ASR action for message:", message.asr, message);

    try {
      const result = await getASRResult();
      setResultData({ transcription: result });
    } catch (error) {
      console.error("ASR error:", error);
      setResultData({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  // Add pronunciation assessment handler
  const handlePronunciationAction = async () => {
    if (resultType === "pronunciation" && showResultPanel) {
      // Toggle off if already showing
      setShowResultPanel(false);
      return;
    }
    console.log("Handling pronunciation action for message:", message);

    // Check if we have the previous message for reference text
    // For now, we'll use a simple prompt. In a real app, you might want to
    // implement a more sophisticated way to get reference text

    const audioUrl = message.content;
    if (!audioUrl) {
      console.error(
        "No audio URL found in voice message for pronunciation assessment"
      );
      setResultData({ error: "No audio URL found in voice message" });
      setIsLoading(false);
      return;
    }

    const referenceText = (await getASRResult()) || "";
    if (!referenceText.trim()) {
      console.error("No reference text found for pronunciation assessment");
      setResultData({
        error: "No reference text found for pronunciation assessment",
      });
      setIsLoading(false);
      return;
    }

    setResultType("pronunciation");
    setShowResultPanel(true);
    setIsLoading(true);

    try {
      // Extract audio URL from message text
      const audioUrl = message.content;
      if (!audioUrl) {
        console.error("No audio URL found in voice message for pronunciation");
        setResultData({ error: "No audio URL found in voice message" });
        setIsLoading(false);
        return;
      }
      let result = message.pronunciation_assessment;
      if (!result) {
        result = await handlePronunciationAssessment(
          message.id,
          audioUrl,
          referenceText.trim(),
          apiKey,
          "en-US" // Default language, could be made configurable
        );
        result = result.result;
      }
      setResultData(result);
    } catch (error) {
      console.error("Pronunciation assessment error:", error);
      setResultData({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendAction = () => {
    if (onResend && message.text && message.id) {
      // Delete the current message first, then resend the text
      onResend(message.text);
    }
  };

  const handleDeleteAction = () => {
    if (onDelete && message.id) {
      // Show confirmation dialog before deleting
      const confirmed = window.confirm(
        "Are you sure you want to delete this message?"
      );
      if (confirmed) {
        onDelete(message.id);
      }
    }
  };

  const handlePlayVoiceMessage = () => {
    if (message.type === "audio") {
      const audioUrl = message.content;
      console.log("Playing voice message:", message);
      if (!audioUrl) {
        console.error("No audio URL found in voice message");
        return;
      }

      // If already playing, stop the current playback
      if (isPlayingVoice && audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        setIsPlayingVoice(false);
        audioRef.current = null;
        return;
      }

      setIsPlayingVoice(true);
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      audio.onended = () => {
        setIsPlayingVoice(false);
        audioRef.current = null;
      };

      audio.onerror = (error) => {
        console.error("Error playing audio:", error);
        setIsPlayingVoice(false);
        audioRef.current = null;
      };

      audio.onloadedmetadata = () => {
        // Set duration if not already set
        if (!message.duration && audio.duration && isFinite(audio.duration)) {
          message.duration = audio.duration;
        }
      };

      audio.play().catch((error) => {
        console.error("Error playing audio:", error);
        setIsPlayingVoice(false);
        audioRef.current = null;
      });
    }
  };

  const renderMessageContent = () => {
    // Handle voice messages
    if (message.type === "audio") {
      const audioUrl = message.content;
      if (!audioUrl) {
        console.error("No audio URL found in voice message");
        return <div className="error">Error: No audio URL found</div>;
      }
      const duration = message.duration || 0;

      // Format duration properly (convert to MM:SS format)
      const formatDuration = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        if (mins > 0) {
          return `${mins}:${secs.toString().padStart(2, "0")}`;
        }
        return `${secs}"`;
      };

      return (
        <div className="voice-message">
          <button
            className={`voice-play-button ${isPlayingVoice ? "playing" : ""}`}
            onClick={handlePlayVoiceMessage}
            title="Play voice message"
          >
            <span className="voice-duration">{formatDuration(duration)}</span>
            <div className="sound-waves">
              <span className="wave wave-1"></span>
              <span className="wave wave-2"></span>
              <span className="wave wave-3"></span>
            </div>
          </button>
        </div>
      );
    }

    // Handle bot messages
    if (message.sender === ChatMessageSenderType.BOT) {
      if (message.isTyping && !message.text) {
        return (
          <div className="typing-indicator">
            <div className="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        );
      }

      // console.log("Rendering bot message:", message);

      const content = message.text || "";

      // For typing messages, update the DOM directly as text arrives
      if (message.isTyping) {
        return (
          <div
            ref={(el) => {
              if (el && messageRef.current !== el) {
                messageRef.current = el;
                // Apply syntax highlighting if needed
                const codeBlocks = el.querySelectorAll("pre code");
                if (codeBlocks) {
                  codeBlocks.forEach((block) => {
                    hljs.highlightElement(block);
                  });
                }
              }
            }}
            className="bot-content"
            dangerouslySetInnerHTML={{ __html: marked.parse(content) }}
          />
        );
      } else {
        // For completed messages, use normal rendering
        return (
          <div
            ref={messageRef}
            className="bot-content"
            dangerouslySetInnerHTML={{ __html: marked.parse(content) }}
          />
        );
      }
    }
    return <div>{message.text}</div>;
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return "";
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  const shouldShowToolbar = () => {
    // Don't show regular toolbar for voice messages, but show regular toolbar for other messages
    return !message.isTyping && message.isChat();
    // return true;
  };

  // Handle system messages differently - as dividing lines
  if (message.sender === ChatMessageSenderType.SYSTEM) {
    const messageType = message.metadata?.messageType || "error";
    return (
      <div className="system-message-divider" data-id={message.id}>
        <div className="divider-line"></div>
        <div className="divider-text">
          {ChatSystemMessage[messageType] || "Unknown system message"}
        </div>
        <div className="divider-line"></div>
      </div>
    );
  }

  return (
    <div className={`message ${message.sender}`} data-id={message.id}>
      {!message.isTyping && (
        <div className="message-timestamp">
          {formatTimestamp(message.timestamp)}
        </div>
      )}
      {renderMessageContent()}

      {shouldShowToolbar() && (
        <MessageToolbar
          sender={message.sender}
          onTTS={handleTTSAction}
          onGrammar={
            message.sender === ChatMessageSenderType.USER
              ? handleGrammarAction
              : null
          }
          onTranslation={handleTranslationAction}
          onDelete={handleDeleteAction}
          onResend={
            message.sender === ChatMessageSenderType.USER
              ? handleResendAction
              : null
          }
          onASR={message.type === "audio" ? handleASRAction : null}
          onPronunciation={
            message.type === "audio" &&
            message.sender === ChatMessageSenderType.USER
              ? handlePronunciationAction
              : null
          }
          isLastMessage={isLastMessage}
          message={message}
          isVoiceMessage={message.type === "audio"}
        />
      )}

      {showResultPanel && (
        <MessageResultPanel
          type={resultType}
          data={resultData}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default Message;
