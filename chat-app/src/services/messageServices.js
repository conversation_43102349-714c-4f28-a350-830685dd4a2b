// Helper function for API calls
import {
  submitTTSRequest,
  getTTSResult,
  submitGrammarCorrectionRequest,
  getGrammarCorrectionResult,
  submitTranslationRequest,
  getTranslationResult,
  submitConversationSummaryRequest,
  getConversationSummaryResult,
} from "./apiServices";
import { API_BASE_URL } from "../config";
import { getApiHeaders, getFileUploadHeaders } from "../utils/userAuth";
import { isString } from "../utils/string";
import { ChatMessageSenderType } from "../types/ChatMessageType";

export const handleTTS = async ({ messageId, text, apiKey }) => {
  if (!text || !text.trim()) return null;

  // Step 1: Submit TTS request
  const data = await submitTTSRequest({ messageId, text, apiKey });
  const taskId = data.task_id;

  // Step 2: Poll for TTS result
  let result = null;
  for (let i = 0; i < 30; i++) {
    const statusData = await getTTSResult(taskId, apiKey);

    if (statusData.status === "completed") {
      // Upload the base64 audio to get URL
      const audioBlob = base64ToBlob(statusData.audio_base64, "audio/mpeg");
      const audioUrl = await uploadAudioBlob(
        audioBlob,
        `chatbot_user_audio_${cacheKey}.mp3`,
        apiKey
      );

      if (audioUrl) {
        result = { audio_url: audioUrl };
      } else {
        // Fallback to base64 if upload fails
        result = { audio: statusData.audio_base64 };
      }
      break;
    } else if (statusData.status === "failed") {
      result = "Error: TTS processing failed";
      break;
    }

    // Wait before checking again
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  if (!result) {
    throw new Error("TTS processing timed out");
  }

  return result;
};

/**
 * Convert base64 string to Blob
 */
function base64ToBlob(base64, mimeType) {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

/**
 * Upload audio blob to backend
 */
async function uploadAudioBlob(audioBlob, filename, apiKey) {
  try {
    const formData = new FormData();
    formData.append("file", audioBlob, filename);

    const response = await fetch(`${API_BASE_URL}/api/upload`, {
      method: "POST",
      headers: getFileUploadHeaders(), // Use specialized headers for file uploads
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status}`);
    }

    const data = await response.json();
    return data.url;
  } catch (error) {
    console.error("Failed to upload audio:", error);
    return null;
  }
}

export const handleGrammarCorrection = async ({
  messageId,
  text,
  coloquial = false,
  apiKey,
}) => {
  if (!text || !text.trim()) return null;

  // Submit grammar suggestion request
  const data = await submitGrammarCorrectionRequest({
    messageId,
    text,
    coloquial,
    apiKey,
  });
  const taskId = data.task_id;

  // Poll for grammar suggestion result
  let result = null;
  let attempts = 0;

  while (attempts < 30) {
    attempts++;

    const statusData = await getGrammarCorrectionResult(taskId, apiKey);

    if (statusData.status === "completed" && statusData.suggestions) {
      result = statusData.suggestions;
      break;
    } else if (statusData.status === "failed") {
      throw new Error("Grammar suggestion processing failed");
    }

    // Wait before checking again
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  if (!result) {
    throw new Error("Grammar suggestion processing timed out");
  }

  return { suggestions: result };
};

export const handleTranslation = async ({ messageId, text, apiKey }) => {
  if (!text || !text.trim()) return null;

  // Submit translation request
  const data = await submitTranslationRequest({ messageId, text, apiKey });
  const taskId = data.task_id;

  // Poll for translation result
  let result = null;
  let attempts = 0;

  while (attempts < 30) {
    attempts++;

    const statusData = await getTranslationResult(taskId, apiKey);

    if (statusData.status === "completed" && statusData.translated_text) {
      result = { translated_text: statusData.translated_text };
      break;
    } else if (statusData.status === "failed") {
      throw new Error("Translation processing failed");
    }

    // Wait before checking again
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  if (!result) {
    throw new Error("Translation processing timed out");
  }

  return result;
};

export const handleASR = async ({ messageId, audioUrl, apiKey }) => {
  if (!audioUrl) return null;

  try {
    const formData = new FormData();
    formData.append("audio_url", audioUrl);
    formData.append("message_id", messageId || "");

    // Step 3: Submit ASR request
    const response = await fetch(`${API_BASE_URL}/api/asr`, {
      method: "POST",
      headers: getFileUploadHeaders(),
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();
    const taskId = data.task_id;

    // Step 4: Poll for ASR result
    let result = null;
    let attempts = 0;

    while (attempts < 30) {
      attempts++;

      const statusResponse = await fetch(
        `${API_BASE_URL}/api/asr/status?task_id=${taskId}`,
        {
          headers: getApiHeaders(),
        }
      );

      if (!statusResponse.ok) {
        throw new Error(`API error: ${statusResponse.status}`);
      }

      const statusData = await statusResponse.json();

      if (statusData.status === "completed" && statusData.result) {
        result = statusData.result;
        break;
      } else if (statusData.status === "failed") {
        result = "Error: No text detected";
        console.error("ASR processing failed:", statusData.error_message);
        break;
      }

      // Wait before checking again
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    if (!result) {
      result = "Error: ASR processing timed out";
    }

    return result;
  } catch (error) {
    console.error("ASR error:", error);
    throw error;
  }
};

export const handlePronunciationAssessment = async (
  messageId,
  audioUrl,
  referenceText,
  apiKey,
  language = "en-US"
) => {
  if (!audioUrl || !referenceText) return null;

  try {
    // Create form data for the API request
    const formData = new FormData();
    formData.append("reference_text", referenceText);
    formData.append("language", language);
    formData.append("audio_url", audioUrl);
    formData.append("message_id", messageId || "");

    // Submit pronunciation assessment request
    const response = await fetch(
      `${API_BASE_URL}/api/pronunciation_assessment`,
      {
        method: "POST",
        headers: getFileUploadHeaders(),
        body: formData,
      }
    );

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.status !== "success") {
      throw new Error(data.error_message || "Pronunciation assessment failed");
    }

    return data;
  } catch (error) {
    console.error("Pronunciation assessment error:", error);
    throw error;
  }
};

export const handleConversationSummary = async (messages, botName, apiKey) => {
  if (!messages || messages.length === 0) return null;

  const grammarResults = [];
  const pronunciationResults = [];

  await Promise.all(
    messages.map(async (msg) => {
      if (msg.sender === ChatMessageSenderType.USER && msg.content) {
        try {
          let messageText = msg.content.trim();
          const isVoiceMsg = msg.type === "audio";
          if (isVoiceMsg) {
            const audioUrl = msg.content;
            const asrResult = await handleASR({
              messageId: msg.id,
              audioUrl,
              apiKey,
            });
            messageText = asrResult.text || asrResult;
            if (
              !messageText ||
              (isString(messageText) && messageText.indexOf("Error:") === 0)
            ) {
              console.warn("ASR failed for message:", msg.text);
              return; // Skip grammar correction if ASR failed
            }

            const pronunciationResult = await handlePronunciationAssessment(
              msg.id,
              audioUrl,
              messageText,
              apiKey
            );
            if (pronunciationResult) {
              pronunciationResults.push(pronunciationResult);
            }
          }
          const grammarResult = await handleGrammarCorrection({
            messageId: msg.id,
            text: messageText,
            coloquial: isVoiceMsg,
            apiKey,
          });
          // console.log("Grammar result:", grammarResult);
          grammarResults.push({
            text: isVoiceMsg ? `[VOICE MESSAGE] ${messageText}` : messageText,
            suggestions: grammarResult.suggestions || [],
          });
        } catch (error) {
          console.error("Grammar correction error:", error);
        }
      }
    })
  );

  // Convert messages to the expected format
  const formattedMessages = messages.map((msg) => ({
    role: msg.sender === ChatMessageSenderType.USER ? "user" : "assistant",
    content: msg.text,
  }));

  // Submit summary request
  const data = await submitConversationSummaryRequest(
    formattedMessages,
    grammarResults,
    pronunciationResults,
    apiKey
  );
  const taskId = data.task_id;

  // Poll for summary result
  let result = null;
  let attempts = 0;

  while (attempts < 30) {
    attempts++;

    const statusData = await getConversationSummaryResult(taskId, apiKey);

    if (statusData.status === "completed" && statusData.summary) {
      result = statusData.summary;
      break;
    } else if (statusData.status === "failed") {
      throw new Error("Conversation summary processing failed");
    }

    // Wait before checking again
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  if (!result) {
    throw new Error("Conversation summary processing timed out");
  }

  return result;
};
