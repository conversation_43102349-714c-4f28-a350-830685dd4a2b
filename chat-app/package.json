{"name": "chat-app", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "crypto-js": "^4.2.0", "highlight.js": "^11.11.1", "jest-junit": "^16.0.0", "marked": "^15.0.12", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.28.0", "react-scripts": "^5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"dev": "react-scripts start --watch", "start": "react-scripts start", "build": "react-scripts build", "build-dev": "rm -rf build && REACT_APP_ENV=development react-scripts build", "build-prod": "rm -rf build && REACT_APP_ENV=production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/index.js", "!src/reportWebVitals.js", "!src/setupTests.js"], "coverageReporters": ["text", "lcov", "cobertura"], "testResultsProcessor": "jest-junit"}, "jest-junit": {"outputDirectory": "./", "outputName": "junit.xml"}, "packageManager": "yarn@4.6.0+sha512.5383cc12567a95f1d668fbe762dfe0075c595b4bfff433be478dbbe24e05251a8e8c3eb992a986667c1d53b6c3a9c85b8398c35a960587fbd9fa3a0915406728"}